using UnityEngine;
using UnityEngine.UIElements;
using BlastingDesign.UI.DaisyUI.Components.Navigation.Tree;
using System.Collections.Generic;

namespace BlastingDesign.Test
{
    /// <summary>
    /// DaisyTree搜索功能测试
    /// 验证使用DaisyInput组件替换TextField后的搜索功能
    /// </summary>
    public class DaisyTreeSearchTest : MonoBehaviour
    {
        [SerializeField] private UIDocument uiDocument;
        private VisualElement root;
        private DaisyTree testTree;

        void Start()
        {
            if (uiDocument == null)
            {
                uiDocument = GetComponent<UIDocument>();
            }

            if (uiDocument != null)
            {
                root = uiDocument.rootVisualElement;
                CreateTestTree();
            }
        }

        private void CreateTestTree()
        {
            // 创建测试树组件
            testTree = new DaisyTree("search-test-tree");
            testTree.SetAllowSearch(true);
            testTree.SetSearchPlaceholder("搜索测试项目...");
            testTree.SetShowActions(true);
            testTree.SetShowIcons(true);
            testTree.SetShowLines(true);

            // 创建测试数据
            var testData = CreateTestData();
            testTree.SetData(testData);

            // 设置搜索事件
            testTree.OnSearchChanged += OnSearchChanged;
            testTree.OnItemSelected += OnItemSelected;

            // 添加到UI
            testTree.style.width = 400;
            testTree.style.height = 500;
            testTree.style.marginTop = 20;
            testTree.style.marginLeft = 20;
            
            root.Add(testTree);

            // 创建测试按钮
            CreateTestButtons();
        }

        private List<DaisyTreeData> CreateTestData()
        {
            var data = new List<DaisyTreeData>();

            // 创建根节点
            var root1 = new DaisyTreeData("root1", "项目根目录", "📁");
            var root2 = new DaisyTreeData("root2", "资源文件夹", "📂");

            // 添加子项
            var src = root1.AddChild("src", "源代码", "💻");
            src.AddChild("main.cs", "主程序", "📄");
            src.AddChild("utils.cs", "工具类", "🔧");
            src.AddChild("config.json", "配置文件", "⚙️");

            var assets = root1.AddChild("assets", "资源文件", "🎨");
            assets.AddChild("textures", "纹理", "🖼️");
            assets.AddChild("models", "模型", "🎭");
            assets.AddChild("sounds", "音频", "🔊");

            var docs = root2.AddChild("docs", "文档", "📚");
            docs.AddChild("readme.md", "说明文档", "📖");
            docs.AddChild("api.md", "API文档", "📋");

            data.Add(root1);
            data.Add(root2);

            return data;
        }

        private void CreateTestButtons()
        {
            var buttonContainer = new VisualElement();
            buttonContainer.style.flexDirection = FlexDirection.Row;
            buttonContainer.style.marginTop = 10;
            buttonContainer.style.marginLeft = 20;

            // 搜索按钮
            var searchButton = new Button(() => testTree.Search("config"));
            searchButton.text = "搜索 'config'";
            searchButton.style.marginRight = 10;
            buttonContainer.Add(searchButton);

            // 清除搜索按钮
            var clearButton = new Button(() => testTree.ClearSearch());
            clearButton.text = "清除搜索";
            clearButton.style.marginRight = 10;
            buttonContainer.Add(clearButton);

            // 搜索源代码按钮
            var searchSrcButton = new Button(() => testTree.Search("源"));
            searchSrcButton.text = "搜索 '源'";
            buttonContainer.Add(searchSrcButton);

            root.Add(buttonContainer);

            // 添加状态显示
            var statusLabel = new Label("状态: 准备就绪");
            statusLabel.name = "status-label";
            statusLabel.style.marginTop = 10;
            statusLabel.style.marginLeft = 20;
            statusLabel.style.color = Color.white;
            root.Add(statusLabel);
        }

        private void OnSearchChanged(string query)
        {
            var statusLabel = root.Q<Label>("status-label");
            if (statusLabel != null)
            {
                statusLabel.text = string.IsNullOrEmpty(query) 
                    ? "状态: 显示所有项目" 
                    : $"状态: 搜索 '{query}'";
            }
            
            Debug.Log($"搜索查询变更: '{query}'");
        }

        private void OnItemSelected(DaisyTreeData item)
        {
            Debug.Log($"选中项目: {item.Text} (ID: {item.Id})");
        }
    }
}
